{"timestamp": 1755359735.387749, "permissions": {"root_access": false, "sudo_access": false, "docker_access": false, "network_access": true, "file_write_access": true, "process_control": true}, "cursor_processes": [{"pid": 3199450, "name": "node", "cmdline": "/root/.local/share/cursor-agent/versions/2025.08.15-dbc8d73/node /root/.local/share/cursor-agent/versions/2025.08.15-dbc8d73/index.js", "status": "sleeping", "memory_mb": 217.45703125}, {"pid": 3216782, "name": "python", "cmdline": "python cursor_agent_debug.py", "status": "running", "memory_mb": 22.72265625}], "mcp_processes": [{"pid": 1252896, "name": "uv", "cmdline": "/home/<USER>/.cherrystudio/bin/uv tool uvx blender-mcp", "status": "sleeping", "memory_mb": 47.30078125}, {"pid": 1252949, "name": "python", "cmdline": "/home/<USER>/.cache/uv/archive-v0/o1S6Xow7ntik1gVZhJqgG/bin/python /home/<USER>/.cache/uv/archive-v0/o1S6Xow7ntik1gVZhJqgG/bin/blender-mcp", "status": "sleeping", "memory_mb": 56.28515625}, {"pid": 1253229, "name": "bun", "cmdline": "/home/<USER>/.cherrystudio/bin/bun x -y @mcpmarket/mcp-auto-install connect --json", "status": "sleeping", "memory_mb": 9.7890625}, {"pid": 1253466, "name": "node", "cmdline": "node /tmp/bunx-1000-@mcpmarket/mcp-auto-install@latest/node_modules/.bin/mcp-auto-install connect --json", "status": "sleeping", "memory_mb": 68.00390625}, {"pid": 1254090, "name": "bun", "cmdline": "/home/<USER>/.cherrystudio/bin/bun x -y mcp-knowledge-graph --memory-path /home/<USER>/.local/share/knowledge-graph/memory.jsonl", "status": "sleeping", "memory_mb": 10.27734375}, {"pid": 1254091, "name": "node", "cmdline": "node /home/<USER>/.nvm/versions/node/v22.18.0/bin/mcp-knowledge-graph --memory-path /home/<USER>/.local/share/knowledge-graph/memory.jsonl", "status": "sleeping", "memory_mb": 54.46484375}, {"pid": 2883226, "name": "sh", "cmdline": "/bin/sh -c uvx blender-mcp", "status": "sleeping", "memory_mb": 1.7109375}, {"pid": 2883227, "name": "uv", "cmdline": "/snap/astral-uv/908/bin/uv tool uvx blender-mcp", "status": "sleeping", "memory_mb": 43.95703125}, {"pid": 2883326, "name": "python", "cmdline": "/home/<USER>/.cache/uv/archive-v0/o1S6Xow7ntik1gVZhJqgG/bin/python /home/<USER>/.cache/uv/archive-v0/o1S6Xow7ntik1gVZhJqgG/bin/blender-mcp", "status": "sleeping", "memory_mb": 56.59765625}, {"pid": 3199728, "name": "bash", "cmdline": "bash /root/.local/bin/cloudbase-mcp", "status": "sleeping", "memory_mb": 3.4609375}, {"pid": 3199734, "name": "node", "cmdline": "/root/.local/share/cloudbase-cli/current/bin/../node /root/.local/share/cloudbase-cli/current/bin/../mcp.js", "status": "sleeping", "memory_mb": 96.3671875}], "network_ports": [{"port": 7890, "address": "::", "pid": null, "process": "Unknown"}, {"port": 8005, "address": "127.0.0.1", "pid": null, "process": "Unknown"}, {"port": 4929, "address": "::", "pid": 2882242, "process": "code"}, {"port": 8000, "address": "127.0.0.1", "pid": null, "process": "Unknown"}, {"port": 8002, "address": "127.0.0.1", "pid": null, "process": "Unknown"}], "mcp_servers": {"github": true, "filesystem": true, "memory": true, "sequential-thinking": true, "git": true, "time": true, "sqlite": true, "fetch": true, "deepwiki": false, "browser-use": false, "blender": true}, "debug_mode_enabled": true, "max_permissions_granted": true}