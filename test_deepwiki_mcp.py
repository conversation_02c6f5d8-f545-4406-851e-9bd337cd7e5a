#!/usr/bin/env python3
"""
测试 DeepWiki MCP 服务器功能的脚本
"""

import asyncio
import json
import aiohttp
import sys
from typing import Dict, Any, List

class DeepWikiMCPTester:
    def __init__(self, server_url: str = "https://mcp.deepwiki.com/sse"):
        self.server_url = server_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_server_connection(self) -> bool:
        """测试服务器连接"""
        try:
            async with self.session.get(self.server_url, timeout=10) as response:
                print(f"✅ 服务器连接成功: {response.status}")
                return response.status == 200
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            return False
    
    async def test_mcp_capabilities(self) -> Dict[str, Any]:
        """测试 MCP 服务器能力"""
        try:
            # 构造 MCP 初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "roots": {
                            "listChanged": True
                        },
                        "sampling": {}
                    },
                    "clientInfo": {
                        "name": "deepwiki-test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            async with self.session.post(
                self.server_url,
                json=init_request,
                headers=headers,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ MCP 初始化成功")
                    print(f"服务器信息: {result.get('result', {}).get('serverInfo', {})}")
                    return result
                else:
                    print(f"❌ MCP 初始化失败: {response.status}")
                    text = await response.text()
                    print(f"响应内容: {text}")
                    return {}
        except Exception as e:
            print(f"❌ MCP 初始化异常: {e}")
            return {}
    
    async def test_list_tools(self) -> List[Dict[str, Any]]:
        """测试获取可用工具列表"""
        try:
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            async with self.session.post(
                self.server_url,
                json=tools_request,
                headers=headers,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    tools = result.get('result', {}).get('tools', [])
                    print(f"✅ 获取工具列表成功，共 {len(tools)} 个工具:")
                    for tool in tools:
                        print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
                    return tools
                else:
                    print(f"❌ 获取工具列表失败: {response.status}")
                    return []
        except Exception as e:
            print(f"❌ 获取工具列表异常: {e}")
            return []
    
    async def test_ask_question(self, repo_url: str = "microsoft/vscode", question: str = "How to create a VS Code extension?") -> Dict[str, Any]:
        """测试问答功能"""
        try:
            ask_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "ask_question",
                    "arguments": {
                        "repo_url": repo_url,
                        "question": question
                    }
                }
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            print(f"🔍 测试问答功能...")
            print(f"仓库: {repo_url}")
            print(f"问题: {question}")
            
            async with self.session.post(
                self.server_url,
                json=ask_request,
                headers=headers,
                timeout=60
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if 'result' in result:
                        print(f"✅ 问答功能测试成功")
                        content = result['result'].get('content', [])
                        if content:
                            for item in content:
                                if item.get('type') == 'text':
                                    print(f"回答: {item.get('text', '')[:200]}...")
                        return result
                    else:
                        print(f"❌ 问答功能返回错误: {result.get('error', 'Unknown error')}")
                        return result
                else:
                    print(f"❌ 问答功能请求失败: {response.status}")
                    text = await response.text()
                    print(f"响应内容: {text}")
                    return {}
        except Exception as e:
            print(f"❌ 问答功能异常: {e}")
            return {}

async def main():
    """主测试函数"""
    print("🚀 开始测试 DeepWiki MCP 服务器...")
    print("=" * 50)
    
    async with DeepWikiMCPTester() as tester:
        # 测试基本连接
        print("\n1. 测试服务器连接...")
        connection_ok = await tester.test_server_connection()
        
        if not connection_ok:
            print("❌ 服务器连接失败，无法继续测试")
            return
        
        # 测试 MCP 初始化
        print("\n2. 测试 MCP 初始化...")
        init_result = await tester.test_mcp_capabilities()
        
        # 测试工具列表
        print("\n3. 测试获取工具列表...")
        tools = await tester.test_list_tools()
        
        # 测试问答功能
        if tools:
            print("\n4. 测试问答功能...")
            await tester.test_ask_question()
        
        print("\n" + "=" * 50)
        print("🎉 DeepWiki MCP 服务器测试完成!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
