# DeepWiki MCP 服务器安装和配置指南

## 概述

DeepWiki MCP 服务器是一个免费的远程 MCP 服务，提供对 GitHub 仓库文档的访问和 AI 问答功能。本指南将帮助您在 Augment 中配置和使用 DeepWiki MCP 服务器。

## 功能特性

DeepWiki MCP 服务器提供以下三个主要工具：

1. **read_wiki_structure** - 获取 GitHub 仓库的文档主题列表
2. **read_wiki_contents** - 查看 GitHub 仓库的文档内容
3. **ask_question** - 向 GitHub 仓库提问并获得 AI 驱动的上下文回答

## 配置状态

✅ **已完成配置**

DeepWiki MCP 服务器已成功添加到您的 Augment 配置文件中：

```json
{
  "mcp": {
    "servers": {
      "deepwiki": {
        "serverUrl": "https://mcp.deepwiki.com/sse",
        "description": "DeepWiki MCP服务器，提供GitHub仓库文档搜索和AI问答功能"
      }
    }
  }
}
```

## 服务器验证

✅ **服务器连接正常**

- 服务器 URL: `https://mcp.deepwiki.com/sse`
- 协议: Server-Sent Events (SSE)
- 状态: 在线并可访问
- 认证: 无需认证（免费公共服务）

## 使用方法

### 1. 在代码编辑器中使用

如果您的代码编辑器支持 MCP 协议（如 Cursor、Windsurf 等），DeepWiki 功能将自动可用。

### 2. 示例用法

以下是一些使用 DeepWiki MCP 服务器的示例：

#### 查询仓库文档结构
```
获取 microsoft/vscode 仓库的文档结构
```

#### 查看特定文档内容
```
查看 microsoft/vscode 仓库中关于扩展开发的文档
```

#### 智能问答
```
如何为 VS Code 创建一个新的扩展？
```

### 3. 支持的仓库格式

- `owner/repo` (例如: `microsoft/vscode`)
- 完整 GitHub URL (例如: `https://github.com/microsoft/vscode`)

## 技术细节

### 协议信息
- **协议版本**: MCP 2024-11-05
- **传输方式**: Server-Sent Events (SSE)
- **端点**: `https://mcp.deepwiki.com/sse`
- **备用端点**: `https://mcp.deepwiki.com/mcp` (Streamable HTTP)

### 兼容性
- ✅ Claude Code
- ✅ Cursor
- ✅ Windsurf
- ✅ 其他支持 MCP 的编辑器

## 故障排除

### 常见问题

1. **服务器无响应**
   - 检查网络连接
   - 确认服务器 URL 正确
   - 重启代码编辑器

2. **功能不可用**
   - 确认编辑器支持 MCP 协议
   - 检查配置文件格式是否正确
   - 重新加载配置

3. **查询失败**
   - 确认仓库名称格式正确
   - 检查仓库是否为公开仓库
   - 尝试使用不同的查询方式

### 配置验证

运行以下命令验证配置：

```bash
python verify_mcp_config.py
```

## 限制说明

- 仅支持公开的 GitHub 仓库
- 服务器由第三方提供，可能有使用限制
- 对于私有仓库，需要使用 Devin MCP 服务器

## 相关资源

- [DeepWiki 官方文档](https://docs.devin.ai/work-with-devin/deepwiki-mcp)
- [Model Context Protocol 规范](https://modelcontextprotocol.io/)
- [Devin MCP 服务器](https://docs.devin.ai/work-with-devin/devin-mcp) (支持私有仓库)

## 下一步

1. 重启您的代码编辑器以加载新配置
2. 尝试使用 DeepWiki 功能查询一些流行的开源项目
3. 探索不同的查询方式和用例

---

**配置完成时间**: 2025-08-16  
**配置状态**: ✅ 成功  
**服务器状态**: ✅ 在线
