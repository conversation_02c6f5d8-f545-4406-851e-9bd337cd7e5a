#!/usr/bin/env python3
"""
验证 MCP 配置文件的脚本
"""

import json
import os
import sys
from pathlib import Path

def load_config(config_path: str) -> dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return {}

def verify_deepwiki_config(config: dict) -> bool:
    """验证 DeepWiki MCP 配置"""
    print("🔍 验证 DeepWiki MCP 配置...")

    # 检查 Augment 格式的配置
    mcp_servers = config.get('mcp', {}).get('servers', {})
    if not mcp_servers:
        # 检查标准 MCP 格式
        mcp_servers = config.get('mcpServers', {})
        if not mcp_servers:
            print("❌ 未找到 mcp.servers 或 mcpServers 配置")
            return False

    # 检查 deepwiki 配置
    deepwiki_config = mcp_servers.get('deepwiki')
    if not deepwiki_config:
        print("❌ 未找到 deepwiki 服务器配置")
        return False

    # 检查必要的字段
    server_url = deepwiki_config.get('serverUrl')
    if not server_url:
        print("❌ deepwiki 配置缺少 serverUrl")
        return False

    if server_url != "https://mcp.deepwiki.com/sse":
        print(f"⚠️  deepwiki serverUrl 不是推荐值: {server_url}")
        print("   推荐值: https://mcp.deepwiki.com/sse")

    print("✅ DeepWiki MCP 配置验证通过")
    print(f"   服务器URL: {server_url}")
    print(f"   描述: {deepwiki_config.get('description', '无描述')}")

    return True

def verify_other_servers(config: dict) -> None:
    """验证其他 MCP 服务器配置"""
    print("\n🔍 检查其他 MCP 服务器...")

    # 检查 Augment 格式的配置
    mcp_servers = config.get('mcp', {}).get('servers', {})
    if not mcp_servers:
        # 检查标准 MCP 格式
        mcp_servers = config.get('mcpServers', {})

    other_servers = {k: v for k, v in mcp_servers.items() if k != 'deepwiki'}

    if not other_servers:
        print("ℹ️  未配置其他 MCP 服务器")
        return

    for name, server_config in other_servers.items():
        print(f"  📦 {name}:")
        if 'command' in server_config:
            print(f"     命令: {server_config['command']}")
            if 'args' in server_config:
                print(f"     参数: {' '.join(server_config['args'])}")
        elif 'serverUrl' in server_config:
            print(f"     URL: {server_config['serverUrl']}")

        if 'description' in server_config:
            print(f"     描述: {server_config['description']}")

def main():
    """主函数"""
    print("🚀 开始验证 MCP 配置...")
    print("=" * 50)
    
    # 查找配置文件
    config_files = [
        "augment_mcp_config_with_env.json",
        "mcp_config.json",
        ".cursor/mcp.json"
    ]
    
    config_found = False
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"📁 找到配置文件: {config_file}")
            config = load_config(config_file)
            
            if config:
                config_found = True
                
                # 验证 DeepWiki 配置
                deepwiki_ok = verify_deepwiki_config(config)
                
                # 验证其他服务器
                verify_other_servers(config)
                
                print("\n" + "=" * 50)
                if deepwiki_ok:
                    print("✅ 配置验证完成！DeepWiki MCP 服务器已正确配置")
                    print("\n📋 使用说明:")
                    print("1. 确保您的代码编辑器支持 MCP 协议")
                    print("2. 重启编辑器以加载新配置")
                    print("3. 在编辑器中尝试使用 DeepWiki 功能")
                    print("\n🔧 可用工具:")
                    print("- read_wiki_structure: 获取仓库文档结构")
                    print("- read_wiki_contents: 查看仓库文档内容")
                    print("- ask_question: 向仓库提问并获得AI回答")
                else:
                    print("❌ 配置验证失败！请检查配置文件")
                break
    
    if not config_found:
        print("❌ 未找到任何 MCP 配置文件")
        print("请确保以下文件之一存在:")
        for config_file in config_files:
            print(f"  - {config_file}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 验证被用户中断")
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        sys.exit(1)
