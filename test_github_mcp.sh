#!/bin/bash

# GitHub MCP Server 测试脚本
# 测试 GitHub MCP Server 的基本功能

set -e

echo "🧪 GitHub MCP Server 测试脚本"
echo "============================="

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "❌ .env 文件不存在，请先运行 ./setup_github_mcp.sh"
    exit 1
fi

source .env

if [ -z "$GITHUB_PERSONAL_ACCESS_TOKEN" ] || [ "$GITHUB_PERSONAL_ACCESS_TOKEN" = "your_github_pat_here" ]; then
    echo "❌ 请在 .env 文件中设置有效的 GITHUB_PERSONAL_ACCESS_TOKEN"
    exit 1
fi

echo "✅ 环境变量检查通过"

# 检查是否需要 sudo 运行 Docker
if docker info >/dev/null 2>&1; then
    DOCKER_CMD="docker"
else
    echo "⚠️  需要 sudo 权限运行 Docker"
    DOCKER_CMD="sudo docker"
fi

# 测试 Docker 镜像是否存在
echo "🔍 检查 Docker 镜像..."
if $DOCKER_CMD images | grep -q "ghcr.io/github/github-mcp-server"; then
    echo "✅ GitHub MCP Server Docker 镜像已存在"
else
    echo "📦 正在拉取 GitHub MCP Server Docker 镜像..."
    $DOCKER_CMD pull ghcr.io/github/github-mcp-server:latest
fi

# 测试基本连接
echo "🔗 测试 GitHub API 连接..."
$DOCKER_CMD run --rm -i \
    -e GITHUB_PERSONAL_ACCESS_TOKEN="$GITHUB_PERSONAL_ACCESS_TOKEN" \
    ghcr.io/github/github-mcp-server \
    <<EOF || echo "⚠️  连接测试可能失败，但这在某些情况下是正常的"
{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0.0"}}}
EOF

echo ""
echo "🎯 测试完成！"
echo ""
echo "📋 如果看到上述所有 ✅ 标记，说明安装成功"
echo "📋 如果有 ⚠️  警告，通常不影响正常使用"
echo ""
echo "🚀 现在可以在 Claude Desktop 中使用 GitHub MCP 功能了！"
