#!/bin/bash

# MCP服务器启动脚本
# 用于启动和管理MCP服务器

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 启动MCP服务器..."
echo "工作目录: $SCRIPT_DIR"

# 检查虚拟环境
if [ ! -d "mcp_env" ]; then
    echo "❌ 虚拟环境不存在，正在创建..."
    python3 -m venv mcp_env
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source mcp_env/bin/activate

# 安装依赖
echo "📦 安装依赖..."
pip install -r requirements.txt 2>/dev/null || pip install mcp mcp-server-filesystem mcp-server-memory

# 启动MCP服务器
echo "🚀 启动MCP服务器..."
python3 start_mcp_servers.py start
