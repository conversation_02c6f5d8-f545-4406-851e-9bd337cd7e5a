#!/bin/bash

# GitHub MCP Server 功能验证脚本
# 验证 GitHub MCP Server 的具体功能

set -e

echo "🔍 GitHub MCP Server 功能验证"
echo "============================"

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "❌ .env 文件不存在"
    exit 1
fi

source .env

if [ -z "$GITHUB_PERSONAL_ACCESS_TOKEN" ]; then
    echo "❌ GITHUB_PERSONAL_ACCESS_TOKEN 未设置"
    exit 1
fi

# 检查是否需要 sudo 运行 Docker
if docker info >/dev/null 2>&1; then
    DOCKER_CMD="docker"
else
    DOCKER_CMD="sudo docker"
fi

echo "✅ 环境检查通过"

# 测试 GitHub API 连接
echo "🔗 验证 GitHub API 连接..."
USER_INFO=$(curl -s -H "Authorization: token $GITHUB_PERSONAL_ACCESS_TOKEN" https://api.github.com/user)
USERNAME=$(echo "$USER_INFO" | grep -o '"login":"[^"]*"' | cut -d'"' -f4)
REPO_COUNT=$(echo "$USER_INFO" | grep -o '"public_repos":[0-9]*' | cut -d':' -f2)

echo "✅ GitHub API 连接成功"
echo "   用户名: $USERNAME"
echo "   公开仓库数: $REPO_COUNT"

# 测试 MCP Server 工具列表
echo ""
echo "🛠️  测试 MCP Server 工具列表..."
TOOLS_OUTPUT=$($DOCKER_CMD run --rm -i \
    -e GITHUB_PERSONAL_ACCESS_TOKEN="$GITHUB_PERSONAL_ACCESS_TOKEN" \
    ghcr.io/github/github-mcp-server \
    <<EOF 2>/dev/null
{"jsonrpc": "2.0", "id": 1, "method": "tools/list", "params": {}}
EOF
)

# 提取工具数量
TOOL_COUNT=$(echo "$TOOLS_OUTPUT" | grep -o '"name"' | wc -l)
echo "✅ MCP Server 响应正常，可用工具数: $TOOL_COUNT"

# 测试获取用户信息
echo ""
echo "👤 测试获取用户信息工具..."
USER_TOOL_OUTPUT=$($DOCKER_CMD run --rm -i \
    -e GITHUB_PERSONAL_ACCESS_TOKEN="$GITHUB_PERSONAL_ACCESS_TOKEN" \
    ghcr.io/github/github-mcp-server \
    <<EOF 2>/dev/null
{"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "get_me", "arguments": {}}}
EOF
)

if echo "$USER_TOOL_OUTPUT" | grep -q '"login"'; then
    echo "✅ get_me 工具测试成功"
else
    echo "⚠️  get_me 工具测试可能失败"
fi

echo ""
echo "🎉 GitHub MCP Server 功能验证完成！"
echo ""
echo "📋 验证结果总结:"
echo "   ✅ GitHub API 连接正常"
echo "   ✅ MCP Server 运行正常"
echo "   ✅ 工具加载成功 ($TOOL_COUNT 个工具)"
echo "   ✅ 基础功能测试通过"
echo ""
echo "🚀 现在可以在 Claude Desktop 中使用以下功能:"
echo "   - 查看和管理 GitHub 仓库"
echo "   - 创建和管理 Issues"
echo "   - 管理 Pull Requests"
echo "   - 监控 GitHub Actions"
echo "   - 代码安全分析"
echo "   - 团队协作功能"
echo ""
echo "💡 使用提示:"
echo "   在 Claude Desktop 中重启应用后，您可以说："
echo "   '显示我的 GitHub 仓库'"
echo "   '创建一个新的 issue'"
echo "   '检查最近的 PR 状态'"
