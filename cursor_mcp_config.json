{"mcpServers": {"github": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "-e", "GITHUB_TOOLSETS", "-e", "GITHUB_READ_ONLY", "-e", "GITHUB_HOST", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}", "GITHUB_TOOLSETS": "${GITHUB_TOOLSETS:-all}", "GITHUB_READ_ONLY": "${GITHUB_READ_ONLY:-0}", "GITHUB_HOST": "${GITHUB_HOST:-https://api.github.com}"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "git": {"command": "/home/<USER>/mcp/mcp_env/bin/python", "args": ["-m", "mcp_server_git", "--repository", "/home/<USER>/mcp"]}, "time": {"command": "/home/<USER>/mcp/mcp_env/bin/python", "args": ["-m", "mcp_server_time"]}, "sqlite": {"command": "/home/<USER>/mcp/mcp_env/bin/python", "args": ["-m", "mcp_server_sqlite"]}, "fetch": {"command": "/home/<USER>/mcp/mcp_env/bin/python", "args": ["-m", "mcp_server_fetch"]}, "deepwiki": {"serverUrl": "https://mcp.deepwiki.com/sse"}, "browser-use": {"command": "/home/<USER>/mcp/browser-use-main/.venv/bin/python", "args": ["-m", "browser_use.mcp"]}, "blender": {"command": "/home/<USER>/mcp/mcp_env/bin/python", "args": ["/home/<USER>/mcp/BlenderMCP/blender_mcp_server.py"]}}}