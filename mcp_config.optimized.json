{"mcpServers": {"filesystem": {"name": "Filesystem (Local)", "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>"], "description": "文件系统操作服务器，限制在用户主目录", "isActive": true}, "knowledge-graph": {"name": "Knowledge Graph (Local)", "type": "stdio", "command": "npx", "args": ["-y", "mcp-knowledge-graph", "--memory-path", "/home/<USER>/.local/share/knowledge-graph/memory.jsonl"], "description": "知识图谱记忆服务器", "isActive": true}, "sequential-thinking": {"name": "Sequential Thinking (Local)", "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "本地顺序思考服务器", "isActive": true}, "puppeteer": {"name": "<PERSON><PERSON><PERSON><PERSON> (Local)", "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "description": "使用Puppeteer进行网页抓取的本地服务器", "isActive": true}, "github": {"name": "GitHub (Local)", "type": "stdio", "command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "-e", "GITHUB_TOOLSETS", "-e", "GITHUB_READ_ONLY", "-e", "GITHUB_HOST", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"}, "description": "GitHub MCP服务器，请在环境变量中配置GITHUB_PERSONAL_ACCESS_TOKEN", "isActive": true}, "deepwiki": {"name": "DeepWiki", "description": "DeepWiki MCP服务器，提供GitHub仓库文档搜索和AI问答功能", "type": "sse", "baseUrl": "https://mcp.deepwiki.com/sse", "isActive": true}, "bing-search-cn": {"name": "必应搜索中文 (ModelScope)", "description": "必应搜索中文", "type": "sse", "baseUrl": "https://mcp.api-inference.modelscope.net/82cc7636d8df41/sse", "isActive": true, "provider": "ModelScope", "providerUrl": "https://www.modelscope.cn/mcp/servers/@@yan5236/bing-cn-mcp-server", "logoUrl": "https://resources.modelscope.cn/studio-cover-prod/studio-cover_c1a4feec-7dd2-4188-bd72-aebde64758f3.png"}, "antvis-charts": {"name": "可视化图表-MCP-Server (ModelScope)", "description": "这是一个基于AntV的模型上下文协议服务器，用于生成图表。", "type": "sse", "baseUrl": "https://mcp.api-inference.modelscope.net/4cee598e62284e/sse", "isActive": true, "provider": "ModelScope", "providerUrl": "https://www.modelscope.cn/mcp/servers/@@antvis/mcp-server-chart", "logoUrl": "https://resources.modelscope.cn/mcp-server-cover/ec4a0829-8d46-4709-88e9-67927e639b18.JPG", "tags": ["antv", "visualization"]}, "cherry-python": {"name": "Python Sandbox (@cherry/python)", "type": "inMemory", "description": "在安全的沙盒环境中执行 Python 代码。", "isActive": true, "provider": "CherryAI"}, "chrome-mcp-server": {"name": "Chrome MCP Server", "type": "streamableHttp", "description": "与本地Chrome浏览器实例连接的服务器", "isActive": true, "longRunning": true, "baseUrl": "http://127.0.0.1:12306/mcp"}, "@modelscope/@modelcontextprotocol/sequentialthinking": {"name": "Sequential Thinking (ModelScope)", "description": "一种MCP服务器实现，它通过结构化的思维过程提供了一种动态且反射性的解决问题的工具。", "type": "sse", "baseUrl": "https://mcp.api-inference.modelscope.net/e43cfb115fd44e/sse", "isActive": false, "provider": "ModelScope"}, "@modelscope/@regenrek/mcp-deepwiki": {"name": "Github的维基百科DeepWiKi (ModelScope)", "description": "📖 MCP 服务器用于获取 deepwiki.com 的最新知识，并在 Cursor 和其他代码编辑器中使用", "type": "sse", "baseUrl": "https://mcp.api-inference.modelscope.net/fce813a0fcb146/sse", "isActive": false, "provider": "ModelScope"}, "@modelscope/@tokenizin-agency/mcp-npx-fetch": {"name": "内容抓取转换器 (ModelScope)", "description": "一个强大的MCP服务器，可以轻松地将网页内容抓取并转换为各种格式（HTML、JSON、Markdown、纯文本）。", "type": "sse", "baseUrl": "https://mcp.api-inference.modelscope.net/6e3903fb098e4a/sse", "isActive": false, "provider": "ModelScope"}, "@cherry/filesystem": {"name": "Filesystem (@cherry/filesystem)", "type": "inMemory", "description": "实现文件系统操作的模型上下文协议（MCP）的 Node.js 服务器", "isActive": false, "provider": "CherryAI", "args": ["/home/<USER>"]}}}