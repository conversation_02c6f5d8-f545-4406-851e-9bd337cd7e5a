#!/bin/bash

# GitHub MCP Server 安装部署脚本
# 作者: MCP Assistant
# 日期: $(date +%Y-%m-%d)

set -e

echo "🚀 GitHub MCP Server 安装部署脚本"
echo "=================================="

# 检查 Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

echo "✅ Docker 已安装: $(docker --version)"

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在，正在创建..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "📝 请编辑 .env 文件并填入您的 GitHub Personal Access Token"
        echo "   编辑命令: nano .env"
        echo ""
        echo "🔗 创建 GitHub PAT: https://github.com/settings/tokens"
        echo "   建议权限: repo, read:packages, read:org"
        exit 1
    else
        echo "❌ .env.example 文件不存在"
        exit 1
    fi
fi

# 加载环境变量
source .env

# 检查 GitHub PAT
if [ -z "$GITHUB_PERSONAL_ACCESS_TOKEN" ] || [ "$GITHUB_PERSONAL_ACCESS_TOKEN" = "your_github_pat_here" ]; then
    echo "❌ 请在 .env 文件中设置有效的 GITHUB_PERSONAL_ACCESS_TOKEN"
    exit 1
fi

echo "✅ GitHub PAT 已配置"

# 拉取 GitHub MCP Server Docker 镜像
echo "📦 正在拉取 GitHub MCP Server Docker 镜像..."

# 检查是否需要 sudo 运行 Docker
if docker info >/dev/null 2>&1; then
    DOCKER_CMD="docker"
else
    echo "⚠️  需要 sudo 权限运行 Docker"
    DOCKER_CMD="sudo docker"
fi

$DOCKER_CMD pull ghcr.io/github/github-mcp-server:latest

echo "✅ Docker 镜像拉取完成"

# 测试连接
echo "🔍 测试 GitHub MCP Server 连接..."
$DOCKER_CMD run --rm -e GITHUB_PERSONAL_ACCESS_TOKEN="$GITHUB_PERSONAL_ACCESS_TOKEN" \
    ghcr.io/github/github-mcp-server --version 2>/dev/null || {
    echo "⚠️  版本检查失败，但这可能是正常的"
}

echo ""
echo "🎉 GitHub MCP Server 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 重启您的 Claude Desktop 应用"
echo "2. 在对话中输入 GitHub PAT 当提示时"
echo "3. 开始使用 GitHub MCP 功能"
echo ""
echo "🔧 可用功能："
echo "   - 仓库管理和代码浏览"
echo "   - Issues 和 PR 管理"
echo "   - GitHub Actions 工作流"
echo "   - 代码安全分析"
echo "   - 团队协作功能"
echo ""
echo "📚 更多信息: https://github.com/github/github-mcp-server"
