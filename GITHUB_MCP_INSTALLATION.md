# GitHub MCP Server 安装部署指南

## 概述

GitHub MCP Server 是 GitHub 官方提供的 Model Context Protocol (MCP) 服务器，它允许 AI 工具直接连接到 GitHub 平台，提供以下功能：

- 🏗️ **仓库管理**: 浏览和查询代码、搜索文件、分析提交
- 🐛 **Issues & PR 自动化**: 创建、更新和管理 Issues 和 Pull Requests
- ⚙️ **CI/CD 智能**: 监控 GitHub Actions 工作流运行
- 🔒 **代码分析**: 检查安全发现、审查 Dependabot 警报
- 👥 **团队协作**: 访问讨论、管理通知、分析团队活动

## 前提条件

- ✅ Docker 已安装并运行
- ✅ Claude Desktop 或其他支持 MCP 的客户端
- ❗ GitHub Personal Access Token (PAT)

## 安装步骤

### 1. 创建 GitHub Personal Access Token

访问 [GitHub Settings > Personal Access Tokens](https://github.com/settings/tokens)

**建议权限范围:**
- `repo` - 仓库操作
- `read:packages` - Docker 镜像访问  
- `read:org` - 组织团队访问

### 2. 配置环境变量

```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

在 `.env` 文件中设置您的 GitHub PAT：
```bash
GITHUB_PERSONAL_ACCESS_TOKEN=your_actual_token_here
```

### 3. 运行安装脚本

```bash
./setup_github_mcp.sh
```

### 4. 测试安装

```bash
./test_github_mcp.sh
```

### 5. 重启 Claude Desktop

重启 Claude Desktop 应用以加载新的 MCP 配置。

## 配置选项

### 工具集配置

您可以通过环境变量控制启用的功能：

```bash
# 只启用特定功能
GITHUB_TOOLSETS="repos,issues,pull_requests,actions"

# 启用所有功能
GITHUB_TOOLSETS="all"

# 只读模式
GITHUB_READ_ONLY=1

# 动态工具发现
GITHUB_DYNAMIC_TOOLSETS=1
```

### GitHub Enterprise 支持

```bash
# GitHub Enterprise Server
GITHUB_HOST=https://your-github-enterprise-server.com

# GitHub Enterprise Cloud with data residency
GITHUB_HOST=https://yoursubdomain.ghe.com
```

## 可用工具集

| 工具集 | 描述 |
|--------|------|
| `context` | 用户和 GitHub 上下文信息 |
| `repos` | 仓库相关操作 |
| `issues` | Issues 管理 |
| `pull_requests` | Pull Request 管理 |
| `actions` | GitHub Actions 工作流 |
| `code_security` | 代码安全分析 |
| `dependabot` | Dependabot 工具 |
| `discussions` | GitHub Discussions |
| `gists` | GitHub Gist 管理 |
| `notifications` | 通知管理 |
| `orgs` | 组织管理 |
| `users` | 用户管理 |
| `secret_protection` | 密钥保护 |

## 使用示例

安装完成后，您可以在 Claude Desktop 中使用自然语言与 GitHub 交互：

- "显示我的最新仓库"
- "创建一个新的 issue"
- "检查最近的 PR 状态"
- "分析代码安全警报"
- "运行 GitHub Actions 工作流"

## 故障排除

### 常见问题

1. **Docker 权限错误**
   ```bash
   sudo usermod -aG docker $USER
   # 然后重新登录
   ```

2. **GitHub PAT 权限不足**
   - 确保 PAT 有必要的权限范围
   - 检查组织的 PAT 策略

3. **网络连接问题**
   ```bash
   # 测试 GitHub API 连接
   curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user
   ```

### 日志查看

```bash
# 查看 Docker 容器日志
docker logs $(docker ps -q --filter ancestor=ghcr.io/github/github-mcp-server)
```

## 安全最佳实践

1. **最小权限原则**: 只授予必要的权限
2. **定期轮换**: 定期更新 PAT
3. **环境隔离**: 为不同项目使用不同的 PAT
4. **文件权限**: 保护包含令牌的配置文件
   ```bash
   chmod 600 .env
   ```

## 更多资源

- [GitHub MCP Server 官方文档](https://github.com/github/github-mcp-server)
- [MCP 协议规范](https://modelcontextprotocol.io/)
- [Claude Desktop MCP 配置](https://claude.ai/docs/mcp)

## 支持

如果遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看 [GitHub Issues](https://github.com/github/github-mcp-server/issues)
3. 参考官方文档
