#!/usr/bin/env python3
"""
测试 DeepWiki MCP 服务器功能的脚本
使用 MCP 客户端库来测试实际功能
"""

import asyncio
import json
import sys
from typing import Dict, Any, List

try:
    import httpx
except ImportError:
    print("❌ 需要安装 httpx: pip install httpx")
    sys.exit(1)

class DeepWikiMCPClient:
    def __init__(self, base_url: str = "https://mcp.deepwiki.com"):
        self.base_url = base_url
        self.session_id = None
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def initialize_session(self) -> bool:
        """初始化 MCP 会话"""
        try:
            # 首先获取 SSE 端点来获取会话ID
            response = await self.client.get(f"{self.base_url}/sse")
            if response.status_code == 200:
                # 从 SSE 响应中提取会话ID
                content = response.text
                for line in content.split('\n'):
                    if line.startswith('data: /sse/message?sessionId='):
                        self.session_id = line.split('sessionId=')[1]
                        print(f"✅ 获取到会话ID: {self.session_id[:20]}...")
                        return True
                
                print("❌ 无法从 SSE 响应中提取会话ID")
                return False
            else:
                print(f"❌ SSE 端点连接失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 初始化会话失败: {e}")
            return False
    
    async def send_mcp_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送 MCP 请求"""
        if not self.session_id:
            raise ValueError("会话未初始化")
        
        request_data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/sse/message?sessionId={self.session_id}",
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ MCP 请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return {}
        except Exception as e:
            print(f"❌ 发送 MCP 请求异常: {e}")
            return {}
    
    async def test_initialize(self) -> bool:
        """测试 MCP 初始化"""
        print("🔍 测试 MCP 初始化...")
        
        result = await self.send_mcp_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {"listChanged": True},
                "sampling": {}
            },
            "clientInfo": {
                "name": "deepwiki-test-client",
                "version": "1.0.0"
            }
        })
        
        if result and 'result' in result:
            print("✅ MCP 初始化成功")
            server_info = result['result'].get('serverInfo', {})
            print(f"   服务器名称: {server_info.get('name', 'Unknown')}")
            print(f"   服务器版本: {server_info.get('version', 'Unknown')}")
            return True
        else:
            print("❌ MCP 初始化失败")
            return False
    
    async def test_list_tools(self) -> List[Dict[str, Any]]:
        """测试获取工具列表"""
        print("\n🔍 测试获取工具列表...")
        
        result = await self.send_mcp_request("tools/list")
        
        if result and 'result' in result:
            tools = result['result'].get('tools', [])
            print(f"✅ 获取到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"   📦 {tool.get('name', 'Unknown')}")
                print(f"      描述: {tool.get('description', 'No description')}")
                if 'inputSchema' in tool:
                    properties = tool['inputSchema'].get('properties', {})
                    if properties:
                        print(f"      参数: {', '.join(properties.keys())}")
            return tools
        else:
            print("❌ 获取工具列表失败")
            return []
    
    async def test_ask_question(self, repo_url: str = "microsoft/vscode", question: str = "How to create a VS Code extension?") -> bool:
        """测试问答功能"""
        print(f"\n🔍 测试问答功能...")
        print(f"   仓库: {repo_url}")
        print(f"   问题: {question}")
        
        result = await self.send_mcp_request("tools/call", {
            "name": "ask_question",
            "arguments": {
                "repo_url": repo_url,
                "question": question
            }
        })
        
        if result and 'result' in result:
            print("✅ 问答功能测试成功")
            content = result['result'].get('content', [])
            if content:
                for item in content:
                    if item.get('type') == 'text':
                        text = item.get('text', '')
                        print(f"   回答: {text[:200]}{'...' if len(text) > 200 else ''}")
            return True
        else:
            print("❌ 问答功能测试失败")
            if result and 'error' in result:
                error = result['error']
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return False

async def main():
    """主测试函数"""
    print("🚀 开始测试 DeepWiki MCP 服务器功能...")
    print("=" * 60)
    
    async with DeepWikiMCPClient() as client:
        # 1. 初始化会话
        print("1. 初始化会话...")
        if not await client.initialize_session():
            print("❌ 会话初始化失败，无法继续测试")
            return
        
        # 2. 测试 MCP 初始化
        print("\n2. 测试 MCP 初始化...")
        if not await client.test_initialize():
            print("❌ MCP 初始化失败，无法继续测试")
            return
        
        # 3. 测试工具列表
        print("\n3. 测试获取工具列表...")
        tools = await client.test_list_tools()
        
        # 4. 测试问答功能（如果有 ask_question 工具）
        if any(tool.get('name') == 'ask_question' for tool in tools):
            print("\n4. 测试问答功能...")
            await client.test_ask_question()
        else:
            print("\n4. ⚠️  未找到 ask_question 工具，跳过问答测试")
        
        print("\n" + "=" * 60)
        print("🎉 DeepWiki MCP 服务器功能测试完成!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
