#!/usr/bin/env python3
"""
验证 Cursor Agent 最高权限设置的脚本
"""

import os
import sys
import json
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class CursorAgentVerifier:
    def __init__(self):
        self.workspace_dir = Path("/home/<USER>/mcp")
        self.cursor_config_dir = Path.home() / ".config" / "Cursor"
        self.cursor_user_dir = self.cursor_config_dir / "User"
        
    def verify_cursor_settings(self):
        """验证 Cursor 设置"""
        logger.info("🔍 验证 Cursor 设置...")
        
        settings_path = self.cursor_user_dir / "settings.json"
        if not settings_path.exists():
            logger.error("  ❌ Cursor 设置文件不存在")
            return False
        
        try:
            with open(settings_path) as f:
                settings = json.load(f)
            
            required_settings = {
                "Lingma.aI Chat.mcpToolsInAgentMode": True,
                "application.experimental.rendererProfiling": True,
                "developer.reload": True,
                "debug.allowBreakpointsEverywhere": True,
                "security.workspace.trust.enabled": False
            }
            
            missing_settings = []
            for key, expected_value in required_settings.items():
                if key not in settings or settings[key] != expected_value:
                    missing_settings.append(key)
            
            if missing_settings:
                logger.warning(f"  ⚠️  缺少或错误的设置: {missing_settings}")
            else:
                logger.info("  ✅ 所有必需设置都正确")
            
            # 检查命令白名单
            command_allowlist = settings.get("Lingma.aI Chat.commandAllowlistInAgentMode", "")
            essential_commands = ["python", "node", "npm", "git", "curl", "bash"]
            missing_commands = [cmd for cmd in essential_commands if cmd not in command_allowlist]
            
            if missing_commands:
                logger.warning(f"  ⚠️  命令白名单中缺少: {missing_commands}")
            else:
                logger.info("  ✅ 命令白名单配置正确")
            
            return len(missing_settings) == 0 and len(missing_commands) == 0
            
        except Exception as e:
            logger.error(f"  ❌ 读取设置文件失败: {e}")
            return False
    
    def verify_mcp_config(self):
        """验证 MCP 配置"""
        logger.info("🔍 验证 MCP 配置...")
        
        mcp_config_path = self.cursor_user_dir / "mcp.json"
        if not mcp_config_path.exists():
            logger.error("  ❌ MCP 配置文件不存在")
            return False
        
        try:
            with open(mcp_config_path) as f:
                config = json.load(f)
            
            servers = config.get("mcpServers", {})
            if not servers:
                logger.error("  ❌ 没有配置 MCP 服务器")
                return False
            
            logger.info(f"  📦 配置了 {len(servers)} 个 MCP 服务器:")
            
            working_servers = 0
            for server_name, server_config in servers.items():
                if "serverUrl" in server_config:
                    # 远程服务器
                    status = self._test_remote_server(server_config["serverUrl"])
                    status_icon = "✅" if status else "❌"
                    logger.info(f"    {status_icon} {server_name} (远程): {server_config['serverUrl']}")
                    if status:
                        working_servers += 1
                elif "command" in server_config:
                    # 本地服务器
                    status = self._test_local_command(server_config["command"])
                    status_icon = "✅" if status else "❌"
                    logger.info(f"    {status_icon} {server_name} (本地): {server_config['command']}")
                    if status:
                        working_servers += 1
                else:
                    logger.warning(f"    ⚠️  {server_name}: 配置格式不正确")
            
            logger.info(f"  📊 可用服务器: {working_servers}/{len(servers)}")
            return working_servers > 0
            
        except Exception as e:
            logger.error(f"  ❌ 读取 MCP 配置失败: {e}")
            return False
    
    def _test_remote_server(self, url):
        """测试远程服务器"""
        try:
            result = subprocess.run(['curl', '-s', '--connect-timeout', '5', url], 
                                  capture_output=True, timeout=10)
            return result.returncode == 0
        except:
            return False
    
    def _test_local_command(self, command):
        """测试本地命令"""
        try:
            if command.startswith('./') or command.startswith('/'):
                return Path(command).exists()
            else:
                result = subprocess.run(['which', command], capture_output=True)
                return result.returncode == 0
        except:
            return False
    
    def verify_permissions(self):
        """验证权限配置"""
        logger.info("🔍 验证权限配置...")
        
        permissions_path = self.workspace_dir / "cursor_permissions.json"
        if not permissions_path.exists():
            logger.warning("  ⚠️  权限配置文件不存在")
            return False
        
        try:
            with open(permissions_path) as f:
                permissions = json.load(f)
            
            required_permissions = {
                "allowNetworkAccess": True,
                "allowFileOperations": True,
                "debugMode": True,
                "maxPrivileges": True
            }
            
            missing_permissions = []
            for key, expected_value in required_permissions.items():
                if key not in permissions or permissions[key] != expected_value:
                    missing_permissions.append(key)
            
            if missing_permissions:
                logger.warning(f"  ⚠️  缺少权限: {missing_permissions}")
                return False
            else:
                logger.info("  ✅ 权限配置正确")
                return True
                
        except Exception as e:
            logger.error(f"  ❌ 读取权限配置失败: {e}")
            return False
    
    def verify_environment(self):
        """验证环境配置"""
        logger.info("🔍 验证环境配置...")
        
        # 检查工作目录
        if not self.workspace_dir.exists():
            logger.error(f"  ❌ 工作目录不存在: {self.workspace_dir}")
            return False
        
        # 检查 Python 环境
        python_path = self.workspace_dir / "mcp_env" / "bin" / "python"
        if python_path.exists():
            logger.info("  ✅ Python 虚拟环境存在")
        else:
            logger.warning("  ⚠️  Python 虚拟环境不存在")
        
        # 检查 Git 仓库
        git_dir = self.workspace_dir / ".git"
        if git_dir.exists():
            logger.info("  ✅ Git 仓库已初始化")
        else:
            logger.warning("  ⚠️  Git 仓库未初始化")
        
        # 检查网络连接
        try:
            result = subprocess.run(['ping', '-c', '1', '8.8.8.8'], 
                                  capture_output=True, timeout=5)
            if result.returncode == 0:
                logger.info("  ✅ 网络连接正常")
                network_ok = True
            else:
                logger.warning("  ⚠️  网络连接异常")
                network_ok = False
        except:
            logger.warning("  ⚠️  无法测试网络连接")
            network_ok = False
        
        return network_ok
    
    def verify_cursor_processes(self):
        """验证 Cursor 进程"""
        logger.info("🔍 验证 Cursor 进程...")
        
        try:
            # 检查 Cursor 进程
            result = subprocess.run(['pgrep', '-f', 'cursor'], capture_output=True, text=True)
            cursor_pids = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            if cursor_pids and cursor_pids[0]:
                logger.info(f"  ✅ 发现 {len(cursor_pids)} 个 Cursor 进程")
                
                # 检查 Cursor Agent 进程
                agent_result = subprocess.run(['pgrep', '-f', 'cursor-agent'], capture_output=True, text=True)
                agent_pids = agent_result.stdout.strip().split('\n') if agent_result.stdout.strip() else []
                
                if agent_pids and agent_pids[0]:
                    logger.info(f"  ✅ 发现 {len(agent_pids)} 个 Cursor Agent 进程")
                    return True
                else:
                    logger.warning("  ⚠️  未发现 Cursor Agent 进程")
                    return False
            else:
                logger.warning("  ⚠️  未发现 Cursor 进程")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ 检查进程失败: {e}")
            return False
    
    def run_verification(self):
        """运行完整验证"""
        logger.info("🚀 开始验证 Cursor Agent 最高权限设置...")
        logger.info("=" * 60)
        
        results = {
            "cursor_settings": self.verify_cursor_settings(),
            "mcp_config": self.verify_mcp_config(),
            "permissions": self.verify_permissions(),
            "environment": self.verify_environment(),
            "cursor_processes": self.verify_cursor_processes()
        }
        
        logger.info("")
        logger.info("📊 验证结果摘要:")
        logger.info("=" * 60)
        
        total_checks = len(results)
        passed_checks = sum(results.values())
        
        for check_name, result in results.items():
            status_icon = "✅" if result else "❌"
            logger.info(f"  {status_icon} {check_name.replace('_', ' ').title()}")
        
        logger.info("")
        logger.info(f"📈 总体状态: {passed_checks}/{total_checks} 项检查通过")
        
        if passed_checks == total_checks:
            logger.info("🎉 所有检查都通过！Cursor Agent 已完全配置为最高权限模式")
            logger.info("")
            logger.info("🚀 可以开始使用 Cursor Agent 进行开发了！")
        elif passed_checks >= total_checks * 0.8:
            logger.info("✅ 大部分检查通过，Cursor Agent 基本可用")
            logger.info("💡 建议修复剩余问题以获得最佳体验")
        else:
            logger.warning("⚠️  多项检查失败，建议重新运行设置脚本")
        
        return results

def main():
    verifier = CursorAgentVerifier()
    results = verifier.run_verification()
    
    # 返回适当的退出码
    passed_checks = sum(results.values())
    total_checks = len(results)
    
    if passed_checks == total_checks:
        sys.exit(0)  # 完全成功
    elif passed_checks >= total_checks * 0.8:
        sys.exit(1)  # 部分成功
    else:
        sys.exit(2)  # 失败

if __name__ == "__main__":
    main()
