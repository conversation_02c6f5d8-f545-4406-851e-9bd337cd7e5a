# GitHub MCP Server 环境变量配置示例
# 复制此文件为 .env 并填入您的实际值

# GitHub Personal Access Token
# 在 https://github.com/settings/tokens 创建
# 建议权限: repo, read:packages, read:org
GITHUB_PERSONAL_ACCESS_TOKEN=*********************************************************************************************

# 可选：GitHub Enterprise Server 主机名
# GITHUB_HOST=https://your-github-enterprise-server.com

# 可选：工具集配置 (默认启用所有工具)
# GITHUB_TOOLSETS=repos,issues,pull_requests,actions,code_security

# 可选：只读模式
# GITHUB_READ_ONLY=1

# 可选：动态工具发现
# GITHUB_DYNAMIC_TOOLSETS=1
