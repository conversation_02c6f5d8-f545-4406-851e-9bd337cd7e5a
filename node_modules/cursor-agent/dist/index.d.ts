/**
 * Cursor AI Tools List
 *
 * This file defines the interfaces for various tools that can be used in task sequences.
 * Each interface represents a specific tool with its configuration options.
 */
export interface CodebaseSearch {
    query: string;
    explanation?: string;
    target_directories?: string[];
}
export interface ReadFile {
    relative_workspace_path: string;
    should_read_entire_file: boolean;
    start_line_one_indexed: number;
    end_line_one_indexed_inclusive: number;
    explanation?: string;
}
export interface RunTerminalCmd {
    command: string;
    is_background: boolean;
    require_user_approval: boolean;
    explanation?: string;
}
export interface ListDir {
    relative_workspace_path: string;
    explanation?: string;
}
export interface GrepSearch {
    query: string;
    case_sensitive?: boolean;
    exclude_pattern?: string;
    include_pattern?: string;
    explanation?: string;
}
export interface EditFile {
    target_file: string;
    instructions: string;
    code_edit: string;
    blocking: boolean;
}
export interface FileSearch {
    query: string;
    explanation: string;
}
export interface DeleteFile {
    target_file: string;
    explanation?: string;
}
export interface Reapply {
    target_file: string;
}
export interface ParallelApply {
    edit_plan: string;
    edit_regions: Array<{
        relative_workspace_path: string;
        start_line?: number;
        end_line?: number;
    }>;
}
export interface Tools {
    codebase_search: CodebaseSearch;
    read_file: ReadFile;
    run_terminal_cmd: RunTerminalCmd;
    list_dir: ListDir;
    grep_search: GrepSearch;
    edit_file: EditFile;
    file_search: FileSearch;
    delete_file: DeleteFile;
    reapply: Reapply;
    parallel_apply: ParallelApply;
}
export interface Task {
    name: keyof Tools;
    parameters: Tools[keyof Tools];
    subtasks?: Task[];
}
export interface TaskSequence {
    tasks: Task[];
}
export declare function createCodebaseSearchTask(parameters: CodebaseSearch, subtasks?: Task[]): Task;
export declare function createReadFileTask(parameters: ReadFile, subtasks?: Task[]): Task;
export declare function createRunTerminalCmdTask(parameters: RunTerminalCmd, subtasks?: Task[]): Task;
export declare function createListDirTask(parameters: ListDir, subtasks?: Task[]): Task;
export declare function createGrepSearchTask(parameters: GrepSearch, subtasks?: Task[]): Task;
export declare function createEditFileTask(parameters: EditFile, subtasks?: Task[]): Task;
export declare function createFileSearchTask(parameters: FileSearch, subtasks?: Task[]): Task;
export declare function createDeleteFileTask(parameters: DeleteFile, subtasks?: Task[]): Task;
export declare function createReapplyTask(parameters: Reapply, subtasks?: Task[]): Task;
export declare function createParallelApplyTask(parameters: ParallelApply, subtasks?: Task[]): Task;
export declare function createTaskSequence(tasks: Task[]): TaskSequence;
export declare const commands: {
    file_analysis: string;
    file_issues: string;
    file_refactor: string;
    file_improve: string;
    file_optimize: string;
    file_debug: string;
    think_deep: string;
    think_refactor: string;
    think_improve: string;
    think_optimize: string;
    do_in_sequence: string;
    do_in_parallel: string;
    suggest_next_task: string;
    suggest_idea: string;
    plan_task: string;
    plan_improve: string;
    bug_find: string;
    bug_fix: string;
};
