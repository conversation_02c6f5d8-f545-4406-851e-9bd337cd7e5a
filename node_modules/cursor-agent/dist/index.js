"use strict";
/**
 * Cursor AI Tools List
 *
 * This file defines the interfaces for various tools that can be used in task sequences.
 * Each interface represents a specific tool with its configuration options.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.commands = void 0;
exports.createCodebaseSearchTask = createCodebaseSearchTask;
exports.createReadFileTask = createReadFileTask;
exports.createRunTerminalCmdTask = createRunTerminalCmdTask;
exports.createListDirTask = createListDirTask;
exports.createGrepSearchTask = createGrepSearchTask;
exports.createEditFileTask = createEditFileTask;
exports.createFileSearchTask = createFileSearchTask;
exports.createDeleteFileTask = createDeleteFileTask;
exports.createReapplyTask = createReapplyTask;
exports.createParallelApplyTask = createParallelApplyTask;
exports.createTaskSequence = createTaskSequence;
// Helper functions to create tasks with type safety
function createCodebaseSearchTask(parameters, subtasks) {
    return {
        name: "codebase_search",
        parameters,
        subtasks,
    };
}
function createReadFileTask(parameters, subtasks) {
    return {
        name: "read_file",
        parameters,
        subtasks,
    };
}
function createRunTerminalCmdTask(parameters, subtasks) {
    return {
        name: "run_terminal_cmd",
        parameters,
        subtasks,
    };
}
function createListDirTask(parameters, subtasks) {
    return {
        name: "list_dir",
        parameters,
        subtasks,
    };
}
function createGrepSearchTask(parameters, subtasks) {
    return {
        name: "grep_search",
        parameters,
        subtasks,
    };
}
function createEditFileTask(parameters, subtasks) {
    return {
        name: "edit_file",
        parameters,
        subtasks,
    };
}
function createFileSearchTask(parameters, subtasks) {
    return {
        name: "file_search",
        parameters,
        subtasks,
    };
}
function createDeleteFileTask(parameters, subtasks) {
    return {
        name: "delete_file",
        parameters,
        subtasks,
    };
}
function createReapplyTask(parameters, subtasks) {
    return {
        name: "reapply",
        parameters,
        subtasks,
    };
}
function createParallelApplyTask(parameters, subtasks) {
    return {
        name: "parallel_apply",
        parameters,
        subtasks,
    };
}
// Task sequence creator
function createTaskSequence(tasks) {
    return { tasks };
}
// Commons command
exports.commands = {
    file_analysis: "Analyze the file.",
    file_issues: "Find issues in the file.",
    file_refactor: "Refactor the file.",
    file_improve: "Improve the file.",
    file_optimize: "Optimize the file.",
    file_debug: "Debug the file.",
    // Think Related
    think_deep: "Think deeply about the file.",
    think_refactor: "Think about refactoring the file.",
    think_improve: "Think about improving the file.",
    think_optimize: "Think about optimizing the file.",
    // sequence related
    do_in_sequence: "Do the sequence of tasks.",
    do_in_parallel: "Do the tasks in parallel.",
    // suggestion related
    suggest_next_task: "Suggest the next task.",
    suggest_idea: "Suggest an idea.",
    // plan related
    plan_task: "Plan the task.",
    plan_improve: "Plan the improvement.",
    // bug related
    bug_find: "Find potential bugs in the file.",
    bug_fix: "Fix the bugs in the file.",
};
