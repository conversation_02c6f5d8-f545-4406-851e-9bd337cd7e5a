"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const index_1 = require("./index");
describe("Task Creation Functions", () => {
    test("createCodebaseSearchTask creates valid task", () => {
        const task = (0, index_1.createCodebaseSearchTask)({
            query: "test query",
            explanation: "test explanation",
        });
        expect(task).toEqual({
            name: "codebase_search",
            parameters: {
                query: "test query",
                explanation: "test explanation",
            },
        });
    });
    test("createReadFileTask creates valid task", () => {
        const task = (0, index_1.createReadFileTask)({
            relative_workspace_path: "test/path",
            should_read_entire_file: true,
            start_line_one_indexed: 1,
            end_line_one_indexed_inclusive: 10,
            explanation: "test explanation",
        });
        expect(task).toEqual({
            name: "read_file",
            parameters: {
                relative_workspace_path: "test/path",
                should_read_entire_file: true,
                start_line_one_indexed: 1,
                end_line_one_indexed_inclusive: 10,
                explanation: "test explanation",
            },
        });
    });
    test("createRunTerminalCmdTask creates valid task", () => {
        const task = (0, index_1.createRunTerminalCmdTask)({
            command: "echo test",
            is_background: false,
            require_user_approval: true,
            explanation: "test explanation",
        });
        expect(task).toEqual({
            name: "run_terminal_cmd",
            parameters: {
                command: "echo test",
                is_background: false,
                require_user_approval: true,
                explanation: "test explanation",
            },
        });
    });
    test("createTaskSequence creates valid sequence", () => {
        const tasks = [
            (0, index_1.createCodebaseSearchTask)({ query: "test" }),
            (0, index_1.createReadFileTask)({
                relative_workspace_path: "test",
                should_read_entire_file: true,
                start_line_one_indexed: 1,
                end_line_one_indexed_inclusive: 10,
            }),
        ];
        const sequence = (0, index_1.createTaskSequence)(tasks);
        expect(sequence).toEqual({ tasks });
    });
    test("tasks support subtasks", () => {
        var _a;
        const subtask = (0, index_1.createListDirTask)({ relative_workspace_path: "test" });
        const mainTask = (0, index_1.createCodebaseSearchTask)({ query: "test" }, [subtask]);
        expect(mainTask.subtasks).toHaveLength(1);
        expect((_a = mainTask.subtasks) === null || _a === void 0 ? void 0 : _a[0]).toEqual(subtask);
    });
    test("createParallelApplyTask creates valid task", () => {
        const task = (0, index_1.createParallelApplyTask)({
            edit_plan: "test plan",
            edit_regions: [
                {
                    relative_workspace_path: "test/path",
                    start_line: 1,
                    end_line: 10,
                },
            ],
        });
        expect(task).toEqual({
            name: "parallel_apply",
            parameters: {
                edit_plan: "test plan",
                edit_regions: [
                    {
                        relative_workspace_path: "test/path",
                        start_line: 1,
                        end_line: 10,
                    },
                ],
            },
        });
    });
});
