{"name": "cursor-agent", "version": "1.0.3", "description": "Task sequence creator for Cursor AI agents", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "test": "jest", "prepublishOnly": "pnpm run build && pnpm test", "prepare": "pnpm run build"}, "keywords": ["cursor", "ai", "agent", "task", "sequence"], "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/zalab-inc/cursor_agent"}, "bugs": {"url": "https://github.com/zalab-inc/cursor_agent/issues"}, "homepage": "https://github.com/zalab-inc/cursor_agent#readme", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.0.0"}}