#!/usr/bin/env python3
"""
Cursor Agent 最高权限设置脚本
简化版本，专注于核心功能
"""

import os
import sys
import json
import subprocess
import logging
import time
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class CursorAgentMaxSetup:
    def __init__(self):
        self.workspace_dir = Path("/home/<USER>/mcp")
        self.cursor_config_dir = Path.home() / ".config" / "Cursor"
        self.cursor_user_dir = self.cursor_config_dir / "User"
        
    def setup_environment(self):
        """设置环境变量"""
        logger.info("🔧 设置环境变量...")
        
        env_vars = {
            'CURSOR_DEBUG': '1',
            'MCP_DEBUG': '1',
            'NODE_ENV': 'development',
            'PYTHONPATH': f"{self.workspace_dir}:{os.environ.get('PYTHONPATH', '')}",
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"  ✅ {key}={value}")
    
    def create_directories(self):
        """创建必要的目录"""
        logger.info("📁 创建必要的目录...")
        
        directories = [
            self.cursor_user_dir,
            self.workspace_dir / "logs",
            self.workspace_dir / "temp",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"  ✅ {directory}")
    
    def update_cursor_settings(self):
        """更新 Cursor 设置"""
        logger.info("⚙️  更新 Cursor 设置...")
        
        settings_path = self.cursor_user_dir / "settings.json"
        
        # 读取现有设置
        if settings_path.exists():
            with open(settings_path) as f:
                settings = json.load(f)
            # 备份
            backup_path = settings_path.with_suffix(f".json.backup.{int(time.time())}")
            with open(backup_path, 'w') as f:
                json.dump(settings, f, indent=2)
            logger.info(f"  📋 已备份到: {backup_path}")
        else:
            settings = {}
        
        # 最高权限设置
        max_privilege_settings = {
            "Lingma.aI Chat.mcpToolsInAgentMode": True,
            "Lingma.aI Chat.commandAllowlistInAgentMode": "node,npm,npx,cd,docker-compose,docker,python,pip,sudo,chmod,chown,systemctl,service,kill,ps,netstat,ss,lsof,curl,wget,git,make,gcc,g++,cmake,ninja,cargo,rustc,go,java,javac,mvn,gradle,bash,sh,zsh,fish,cat,ls,cp,mv,rm,mkdir,rmdir,touch,find,grep,sed,awk,tar,zip,unzip",
            "application.experimental.rendererProfiling": True,
            "developer.reload": True,
            "extensions.autoUpdate": False,
            "telemetry.enableTelemetry": False,
            "workbench.developer.experiments.enabled": True,
            "debug.allowBreakpointsEverywhere": True,
            "debug.console.acceptSuggestionOnEnter": "on",
            "terminal.integrated.allowChords": False,
            "terminal.integrated.commandsToSkipShell": [],
            "security.workspace.trust.enabled": False,
            "extensions.ignoreRecommendations": True,
            "workbench.startupEditor": "none",
            "github.copilot.enable": {"*": False},
            "git.suggestSmartCommit": False,
            "editor.fontSize": 32,
            "editor.formatOnSave": True,
            "accessibility.dimUnfocused.enabled": True,
            "workbench.colorTheme": "Visual Studio Dark"
        }
        
        settings.update(max_privilege_settings)
        
        with open(settings_path, 'w') as f:
            json.dump(settings, f, indent=2)
        
        logger.info("  ✅ Cursor 设置已更新")
    
    def create_mcp_config(self):
        """创建 MCP 配置"""
        logger.info("📦 创建 MCP 配置...")
        
        mcp_config_path = self.cursor_user_dir / "mcp.json"
        
        mcp_config = {
            "mcpServers": {
                "filesystem": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>"]
                },
                "memory": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-memory"]
                },
                "sequential-thinking": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
                },
                "deepwiki": {
                    "serverUrl": "https://mcp.deepwiki.com/sse"
                },
                "time": {
                    "command": f"{self.workspace_dir}/mcp_env/bin/python",
                    "args": ["-m", "mcp_server_time"]
                },
                "fetch": {
                    "command": f"{self.workspace_dir}/mcp_env/bin/python",
                    "args": ["-m", "mcp_server_fetch"]
                }
            }
        }
        
        # 如果 Git 仓库存在，添加 Git 服务器
        if (self.workspace_dir / ".git").exists():
            mcp_config["mcpServers"]["git"] = {
                "command": f"{self.workspace_dir}/mcp_env/bin/python",
                "args": ["-m", "mcp_server_git", "--repository", str(self.workspace_dir)]
            }
        
        with open(mcp_config_path, 'w') as f:
            json.dump(mcp_config, f, indent=2)
        
        logger.info(f"  ✅ MCP 配置已保存到: {mcp_config_path}")
    
    def create_permissions_config(self):
        """创建权限配置"""
        logger.info("🔓 创建权限配置...")
        
        permissions_config = {
            "allowedCommands": ["*"],
            "allowedPaths": ["/home/<USER>", "/tmp", "/var/tmp", "/opt", "/usr/local"],
            "allowNetworkAccess": True,
            "allowSystemCalls": True,
            "allowFileOperations": True,
            "allowProcessControl": True,
            "debugMode": True,
            "maxPrivileges": True,
            "sudoAccess": False,
            "dockerAccess": False,
            "description": "Cursor Agent 最高权限配置"
        }
        
        permissions_path = self.workspace_dir / "cursor_permissions.json"
        with open(permissions_path, 'w') as f:
            json.dump(permissions_config, f, indent=2)
        
        logger.info(f"  ✅ 权限配置已保存到: {permissions_path}")
    
    def check_status(self):
        """检查状态"""
        logger.info("📊 检查状态...")
        
        # 检查 Cursor 进程
        try:
            result = subprocess.run(['pgrep', '-f', 'cursor'], capture_output=True, text=True)
            cursor_pids = result.stdout.strip().split('\n') if result.stdout.strip() else []
            logger.info(f"  🖥️  Cursor 进程: {len(cursor_pids)} 个")
        except:
            logger.info("  🖥️  Cursor 进程: 检查失败")
        
        # 检查配置文件
        config_files = [
            self.cursor_user_dir / "settings.json",
            self.cursor_user_dir / "mcp.json",
            self.workspace_dir / "cursor_permissions.json"
        ]
        
        for config_file in config_files:
            status = "✅ 存在" if config_file.exists() else "❌ 缺失"
            logger.info(f"  📄 {config_file.name}: {status}")
        
        # 检查网络连接
        try:
            result = subprocess.run(['curl', '-s', '--connect-timeout', '5', 'https://mcp.deepwiki.com/sse'], 
                                  capture_output=True, timeout=10)
            network_status = "✅ 正常" if result.returncode == 0 else "❌ 异常"
        except:
            network_status = "❌ 异常"
        
        logger.info(f"  🌐 网络连接: {network_status}")
    
    def run_setup(self):
        """运行完整设置"""
        logger.info("🚀 开始 Cursor Agent 最高权限设置...")
        logger.info("=" * 60)
        
        try:
            self.setup_environment()
            self.create_directories()
            self.update_cursor_settings()
            self.create_mcp_config()
            self.create_permissions_config()
            self.check_status()
            
            logger.info("")
            logger.info("🎉 Cursor Agent 最高权限设置完成！")
            logger.info("=" * 60)
            logger.info("💡 下一步:")
            logger.info("  1. 重启 Cursor 编辑器")
            logger.info("  2. 在 Cursor 中测试 MCP 功能")
            logger.info("  3. 使用 Agent 模式进行开发")
            logger.info("")
            logger.info("🔧 可用命令:")
            logger.info("  - 所有系统命令和工具")
            logger.info("  - 文件系统操作")
            logger.info("  - 网络访问")
            logger.info("  - 进程控制")
            logger.info("  - Git 操作")
            logger.info("  - DeepWiki 查询")
            
        except Exception as e:
            logger.error(f"设置过程中发生错误: {e}")
            sys.exit(1)

def main():
    setup = CursorAgentMaxSetup()
    setup.run_setup()

if __name__ == "__main__":
    main()
