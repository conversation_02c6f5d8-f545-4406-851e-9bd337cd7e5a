#!/bin/bash

# Cursor Agent 最高权限启动脚本
# 提供完整的系统访问和调试功能

set -e

echo "🚀 启动 Cursor Agent 最高权限模式..."
echo "=================================================="

# 设置环境变量
export CURSOR_DEBUG=1
export MCP_DEBUG=1
export NODE_ENV=development
export PYTHONPATH="/home/<USER>/mcp:$PYTHONPATH"
export PATH="/home/<USER>/mcp/mcp_env/bin:$PATH"

# 设置工作目录
cd /home/<USER>/mcp

echo "📁 当前工作目录: $(pwd)"
echo "🔧 Python 路径: $(which python)"
echo "🔧 Node 路径: $(which node || echo 'Node not found')"
echo "🔧 NPX 路径: $(which npx || echo 'NPX not found')"

# 检查并启动虚拟环境
if [ -f "mcp_env/bin/activate" ]; then
    echo "🐍 激活 Python 虚拟环境..."
    source mcp_env/bin/activate
else
    echo "⚠️  Python 虚拟环境未找到"
fi

# 设置文件权限
echo "🔓 设置文件权限..."
chmod -R 755 /home/<USER>/mcp/ 2>/dev/null || true
chmod +x /home/<USER>/mcp/*.py 2>/dev/null || true
chmod +x /home/<USER>/mcp/*.sh 2>/dev/null || true

# 创建必要的目录
mkdir -p /home/<USER>/.config/Cursor/User
mkdir -p /home/<USER>/mcp/logs
mkdir -p /home/<USER>/mcp/temp

# 检查 Cursor 进程
echo "🔍 检查 Cursor 进程..."
CURSOR_PIDS=$(pgrep -f "cursor" || true)
if [ -n "$CURSOR_PIDS" ]; then
    echo "✅ 发现 Cursor 进程: $CURSOR_PIDS"
else
    echo "⚠️  未发现 Cursor 进程"
fi

# 检查 Cursor Agent 进程
echo "🔍 检查 Cursor Agent 进程..."
AGENT_PIDS=$(pgrep -f "cursor-agent" || true)
if [ -n "$AGENT_PIDS" ]; then
    echo "✅ 发现 Cursor Agent 进程: $AGENT_PIDS"
else
    echo "⚠️  未发现 Cursor Agent 进程"
fi

# 启动 MCP 服务器
echo "🔧 启动 MCP 服务器..."

# 启动本地 Python MCP 服务器
echo "  📦 启动 Python MCP 服务器..."
python -m mcp_server_git --repository /home/<USER>/mcp &
python -m mcp_server_time &
python -m mcp_server_sqlite &
python -m mcp_server_fetch &

# 等待服务器启动
sleep 2

# 检查 MCP 进程
echo "🔍 检查 MCP 进程..."
MCP_PIDS=$(pgrep -f "mcp_server" || true)
if [ -n "$MCP_PIDS" ]; then
    echo "✅ 发现 MCP 服务器进程: $MCP_PIDS"
else
    echo "⚠️  未发现 MCP 服务器进程"
fi

# 测试网络连接
echo "🌐 测试网络连接..."
if curl -s --connect-timeout 5 https://mcp.deepwiki.com/sse > /dev/null; then
    echo "✅ DeepWiki MCP 服务器连接正常"
else
    echo "❌ DeepWiki MCP 服务器连接失败"
fi

# 创建权限配置文件
echo "📝 创建权限配置文件..."
cat > cursor_permissions.json << EOF
{
  "allowedCommands": ["*"],
  "allowedPaths": ["/home/<USER>", "/tmp", "/var/tmp", "/opt", "/usr/local"],
  "allowNetworkAccess": true,
  "allowSystemCalls": true,
  "allowFileOperations": true,
  "allowProcessControl": true,
  "debugMode": true,
  "maxPrivileges": true,
  "sudoAccess": false,
  "dockerAccess": false
}
EOF

# 更新 Cursor 设置
echo "⚙️  更新 Cursor 设置..."
CURSOR_SETTINGS="/home/<USER>/.config/Cursor/User/settings.json"
if [ -f "$CURSOR_SETTINGS" ]; then
    # 备份原设置
    cp "$CURSOR_SETTINGS" "$CURSOR_SETTINGS.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 使用 Python 更新设置
    python << EOF
import json
import os

settings_file = "$CURSOR_SETTINGS"
try:
    with open(settings_file, 'r') as f:
        settings = json.load(f)
except:
    settings = {}

# 添加最高权限设置
max_privilege_settings = {
    "Lingma.aI Chat.mcpToolsInAgentMode": True,
    "Lingma.aI Chat.commandAllowlistInAgentMode": "node,npm,npx,cd,docker-compose,docker,python,pip,sudo,chmod,chown,systemctl,service,kill,ps,netstat,ss,lsof,curl,wget,git,make,gcc,g++,cmake,ninja,cargo,rustc,go,java,javac,mvn,gradle",
    "application.experimental.rendererProfiling": True,
    "developer.reload": True,
    "extensions.autoUpdate": False,
    "telemetry.enableTelemetry": False,
    "workbench.developer.experiments.enabled": True,
    "debug.allowBreakpointsEverywhere": True,
    "debug.console.acceptSuggestionOnEnter": "on",
    "terminal.integrated.allowChords": False,
    "terminal.integrated.commandsToSkipShell": [],
    "security.workspace.trust.enabled": False,
    "extensions.ignoreRecommendations": True
}

settings.update(max_privilege_settings)

with open(settings_file, 'w') as f:
    json.dump(settings, f, indent=2)

print("✅ Cursor 设置已更新")
EOF
else
    echo "⚠️  Cursor 设置文件不存在，创建新文件..."
    cat > "$CURSOR_SETTINGS" << EOF
{
  "Lingma.aI Chat.mcpToolsInAgentMode": true,
  "Lingma.aI Chat.commandAllowlistInAgentMode": "node,npm,npx,cd,docker-compose,docker,python,pip,sudo,chmod,chown,systemctl,service,kill,ps,netstat,ss,lsof,curl,wget,git,make,gcc,g++,cmake,ninja,cargo,rustc,go,java,javac,mvn,gradle",
  "application.experimental.rendererProfiling": true,
  "developer.reload": true,
  "extensions.autoUpdate": false,
  "telemetry.enableTelemetry": false,
  "workbench.developer.experiments.enabled": true,
  "debug.allowBreakpointsEverywhere": true,
  "security.workspace.trust.enabled": false
}
EOF
fi

# 显示状态摘要
echo ""
echo "📊 状态摘要:"
echo "=================================================="
echo "🔧 工作目录: $(pwd)"
echo "🐍 Python 环境: $(python --version 2>/dev/null || echo 'Not available')"
echo "📦 Node 环境: $(node --version 2>/dev/null || echo 'Not available')"
echo "🌐 网络连接: $(curl -s --connect-timeout 3 https://google.com > /dev/null && echo '✅ 正常' || echo '❌ 异常')"
echo "📁 MCP 配置: $([ -f '/home/<USER>/.config/Cursor/User/mcp.json' ] && echo '✅ 存在' || echo '❌ 缺失')"
echo "⚙️  Cursor 设置: $([ -f '/home/<USER>/.config/Cursor/User/settings.json' ] && echo '✅ 存在' || echo '❌ 缺失')"

# 显示运行中的进程
echo ""
echo "🔍 运行中的相关进程:"
echo "Cursor 进程: $(pgrep -c -f 'cursor' || echo '0')"
echo "MCP 进程: $(pgrep -c -f 'mcp' || echo '0')"
echo "Python 进程: $(pgrep -c -f 'python.*mcp' || echo '0')"

echo ""
echo "🎉 Cursor Agent 最高权限模式启动完成！"
echo "=================================================="
echo "💡 提示:"
echo "  - 所有 MCP 服务器已启动"
echo "  - Cursor 设置已优化为最高权限"
echo "  - 调试模式已启用"
echo "  - 可以使用所有系统命令和工具"
echo ""
echo "🔧 如需重启 Cursor，请运行: killall cursor && cursor"
echo "📊 查看详细状态: python cursor_agent_debug.py"
echo ""
