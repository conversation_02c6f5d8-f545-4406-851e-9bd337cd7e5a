#!/usr/bin/env python3
"""
Cursor Agent 高权限调试脚本
提供全面的系统访问和调试功能
"""

import os
import sys
import json
import subprocess
import psutil
import socket
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import asyncio

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cursor_agent_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CursorAgentDebugger:
    def __init__(self):
        self.cursor_config_dir = Path.home() / ".config" / "Cursor"
        self.mcp_config_path = self.cursor_config_dir / "User" / "mcp.json"
        self.workspace_dir = Path("/home/<USER>/mcp")
        self.processes = {}
        
    def check_system_permissions(self) -> Dict[str, bool]:
        """检查系统权限"""
        logger.info("🔍 检查系统权限...")
        
        permissions = {
            "root_access": os.geteuid() == 0,
            "sudo_access": self._check_sudo_access(),
            "docker_access": self._check_docker_access(),
            "network_access": self._check_network_access(),
            "file_write_access": self._check_file_write_access(),
            "process_control": self._check_process_control()
        }
        
        for perm, status in permissions.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"  {status_icon} {perm}: {status}")
        
        return permissions
    
    def _check_sudo_access(self) -> bool:
        """检查 sudo 访问权限"""
        try:
            result = subprocess.run(
                ["sudo", "-n", "true"], 
                capture_output=True, 
                timeout=5
            )
            return result.returncode == 0
        except:
            return False
    
    def _check_docker_access(self) -> bool:
        """检查 Docker 访问权限"""
        try:
            result = subprocess.run(
                ["docker", "ps"], 
                capture_output=True, 
                timeout=10
            )
            return result.returncode == 0
        except:
            return False
    
    def _check_network_access(self) -> bool:
        """检查网络访问权限"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(("8.8.8.8", 53))
            sock.close()
            return result == 0
        except:
            return False
    
    def _check_file_write_access(self) -> bool:
        """检查文件写入权限"""
        try:
            test_file = Path("/tmp/cursor_agent_test")
            test_file.write_text("test")
            test_file.unlink()
            return True
        except:
            return False
    
    def _check_process_control(self) -> bool:
        """检查进程控制权限"""
        try:
            # 尝试获取进程列表
            processes = list(psutil.process_iter(['pid', 'name']))
            return len(processes) > 0
        except:
            return False
    
    def scan_cursor_processes(self) -> List[Dict[str, Any]]:
        """扫描 Cursor 相关进程"""
        logger.info("🔍 扫描 Cursor 相关进程...")
        
        cursor_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'memory_info']):
            try:
                if 'cursor' in proc.info['name'].lower() or \
                   any('cursor' in arg.lower() for arg in proc.info['cmdline'] or []):
                    cursor_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': ' '.join(proc.info['cmdline'] or []),
                        'status': proc.info['status'],
                        'memory_mb': proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        logger.info(f"找到 {len(cursor_processes)} 个 Cursor 进程")
        for proc in cursor_processes:
            logger.info(f"  📦 PID {proc['pid']}: {proc['name']} ({proc['status']}) - {proc['memory_mb']:.1f}MB")
        
        return cursor_processes
    
    def scan_mcp_processes(self) -> List[Dict[str, Any]]:
        """扫描 MCP 相关进程"""
        logger.info("🔍 扫描 MCP 相关进程...")
        
        mcp_processes = []
        mcp_keywords = ['mcp', 'modelcontextprotocol', 'server-', 'deepwiki', 'github-mcp']
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'memory_info']):
            try:
                cmdline_str = ' '.join(proc.info['cmdline'] or []).lower()
                if any(keyword in cmdline_str for keyword in mcp_keywords):
                    mcp_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': ' '.join(proc.info['cmdline'] or []),
                        'status': proc.info['status'],
                        'memory_mb': proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        logger.info(f"找到 {len(mcp_processes)} 个 MCP 进程")
        for proc in mcp_processes:
            logger.info(f"  🔧 PID {proc['pid']}: {proc['name']} ({proc['status']}) - {proc['memory_mb']:.1f}MB")
        
        return mcp_processes
    
    def check_network_ports(self) -> List[Dict[str, Any]]:
        """检查网络端口占用"""
        logger.info("🔍 检查网络端口占用...")
        
        listening_ports = []
        for conn in psutil.net_connections(kind='inet'):
            if conn.status == psutil.CONN_LISTEN:
                try:
                    proc = psutil.Process(conn.pid) if conn.pid else None
                    listening_ports.append({
                        'port': conn.laddr.port,
                        'address': conn.laddr.ip,
                        'pid': conn.pid,
                        'process': proc.name() if proc else 'Unknown'
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    listening_ports.append({
                        'port': conn.laddr.port,
                        'address': conn.laddr.ip,
                        'pid': conn.pid,
                        'process': 'Unknown'
                    })
        
        # 过滤常见的 MCP 端口范围
        mcp_ports = [p for p in listening_ports if 3000 <= p['port'] <= 9999]
        
        logger.info(f"找到 {len(mcp_ports)} 个可能的 MCP 端口")
        for port_info in mcp_ports:
            logger.info(f"  🌐 端口 {port_info['port']} ({port_info['address']}) - PID {port_info['pid']} ({port_info['process']})")
        
        return mcp_ports
    
    def test_mcp_servers(self) -> Dict[str, bool]:
        """测试 MCP 服务器连接"""
        logger.info("🔍 测试 MCP 服务器连接...")
        
        if not self.mcp_config_path.exists():
            logger.warning("MCP 配置文件不存在")
            return {}
        
        try:
            with open(self.mcp_config_path) as f:
                config = json.load(f)
        except Exception as e:
            logger.error(f"读取 MCP 配置失败: {e}")
            return {}
        
        servers = config.get('mcpServers', {})
        results = {}
        
        for server_name, server_config in servers.items():
            logger.info(f"测试服务器: {server_name}")
            
            if 'serverUrl' in server_config:
                # 远程服务器
                results[server_name] = self._test_remote_server(server_config['serverUrl'])
            elif 'command' in server_config:
                # 本地服务器
                results[server_name] = self._test_local_server(server_config)
            else:
                results[server_name] = False
        
        return results
    
    def _test_remote_server(self, url: str) -> bool:
        """测试远程服务器"""
        try:
            import requests
            response = requests.get(url, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def _test_local_server(self, config: Dict[str, Any]) -> bool:
        """测试本地服务器"""
        try:
            command = config['command']
            args = config.get('args', [])
            
            # 简单的命令存在性检查
            if command.startswith('./') or command.startswith('/'):
                return Path(command).exists()
            else:
                result = subprocess.run(['which', command], capture_output=True)
                return result.returncode == 0
        except:
            return False
    
    def enable_debug_mode(self) -> bool:
        """启用调试模式"""
        logger.info("🔧 启用 Cursor Agent 调试模式...")
        
        try:
            # 更新 Cursor 设置以启用调试
            settings_path = self.cursor_config_dir / "User" / "settings.json"
            
            if settings_path.exists():
                with open(settings_path) as f:
                    settings = json.load(f)
            else:
                settings = {}
            
            # 添加调试设置
            debug_settings = {
                "Lingma.aI Chat.mcpToolsInAgentMode": True,
                "Lingma.aI Chat.commandAllowlistInAgentMode": "node,npm,npx,cd,docker-compose,docker,python,pip,sudo,chmod,chown,systemctl,service,kill,ps,netstat,ss,lsof",
                "application.experimental.rendererProfiling": True,
                "developer.reload": True,
                "extensions.autoUpdate": False,
                "telemetry.enableTelemetry": False,
                "workbench.developer.experiments.enabled": True
            }
            
            settings.update(debug_settings)
            
            with open(settings_path, 'w') as f:
                json.dump(settings, f, indent=2)
            
            logger.info("✅ 调试模式已启用")
            return True
            
        except Exception as e:
            logger.error(f"启用调试模式失败: {e}")
            return False
    
    def grant_maximum_permissions(self) -> bool:
        """赋予最高权限"""
        logger.info("🔓 赋予最高权限...")
        
        try:
            # 设置文件权限
            os.chmod(self.workspace_dir, 0o755)
            
            # 设置环境变量
            os.environ['CURSOR_DEBUG'] = '1'
            os.environ['MCP_DEBUG'] = '1'
            os.environ['NODE_ENV'] = 'development'
            
            # 创建权限配置文件
            permissions_config = {
                "allowedCommands": ["*"],
                "allowedPaths": ["/home/<USER>", "/tmp", "/var/tmp"],
                "allowNetworkAccess": True,
                "allowSystemCalls": True,
                "debugMode": True
            }
            
            permissions_path = self.workspace_dir / "cursor_permissions.json"
            with open(permissions_path, 'w') as f:
                json.dump(permissions_config, f, indent=2)
            
            logger.info("✅ 最高权限已赋予")
            return True
            
        except Exception as e:
            logger.error(f"赋予权限失败: {e}")
            return False
    
    def run_comprehensive_debug(self) -> Dict[str, Any]:
        """运行综合调试"""
        logger.info("🚀 开始 Cursor Agent 综合调试...")
        
        debug_results = {
            "timestamp": time.time(),
            "permissions": self.check_system_permissions(),
            "cursor_processes": self.scan_cursor_processes(),
            "mcp_processes": self.scan_mcp_processes(),
            "network_ports": self.check_network_ports(),
            "mcp_servers": self.test_mcp_servers(),
            "debug_mode_enabled": self.enable_debug_mode(),
            "max_permissions_granted": self.grant_maximum_permissions()
        }
        
        # 保存调试结果
        debug_file = self.workspace_dir / "cursor_debug_results.json"
        with open(debug_file, 'w') as f:
            json.dump(debug_results, f, indent=2, default=str)
        
        logger.info(f"📊 调试结果已保存到: {debug_file}")
        
        return debug_results

def main():
    """主函数"""
    print("🚀 Cursor Agent 高权限调试器")
    print("=" * 60)
    
    debugger = CursorAgentDebugger()
    
    try:
        results = debugger.run_comprehensive_debug()
        
        print("\n📊 调试摘要:")
        print(f"  系统权限: {sum(results['permissions'].values())}/{len(results['permissions'])}")
        print(f"  Cursor 进程: {len(results['cursor_processes'])}")
        print(f"  MCP 进程: {len(results['mcp_processes'])}")
        print(f"  网络端口: {len(results['network_ports'])}")
        print(f"  MCP 服务器: {sum(results['mcp_servers'].values())}/{len(results['mcp_servers'])}")
        print(f"  调试模式: {'✅' if results['debug_mode_enabled'] else '❌'}")
        print(f"  最高权限: {'✅' if results['max_permissions_granted'] else '❌'}")
        
        print("\n🎉 调试完成！Cursor Agent 已配置为最高权限模式")
        
    except Exception as e:
        logger.error(f"调试过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
